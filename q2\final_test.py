# Final verification of the C++ algorithm logic

def solve_atlanta(n, k, edges):
    from collections import defaultdict
    
    # Build adjacency list
    adj = defaultdict(list)
    for u, v, w in edges:
        adj[u].append((v, w))
        adj[v].append((u, w))
    
    # Find bridges using <PERSON><PERSON><PERSON>'s algorithm
    visited = [False] * n
    disc = [0] * n
    low = [0] * n
    parent = [-1] * n
    bridges = set()
    timer = [0]
    
    def bridge_util(u):
        visited[u] = True
        disc[u] = low[u] = timer[0]
        timer[0] += 1
        
        for v, w in adj[u]:
            if not visited[v]:
                parent[v] = u
                bridge_util(v)
                low[u] = min(low[u], low[v])
                
                if low[v] > disc[u]:
                    bridges.add((min(u, v), max(u, v)))
            elif v != parent[u]:
                low[u] = min(low[u], disc[v])
    
    for i in range(n):
        if not visited[i]:
            bridge_util(i)
    
    # Find connected components without bridge edges
    vis = [False] * n
    components = []
    
    def dfs(u, component):
        vis[u] = True
        component.append(u)
        
        for v, w in adj[u]:
            edge_pair = (min(u, v), max(u, v))
            if not vis[v] and edge_pair not in bridges:
                dfs(v, component)
    
    for i in range(n):
        if not vis[i]:
            component = []
            dfs(i, component)
            if len(component) > 1:
                components.append(component)
    
    if not components:
        return 0
    
    # Find the largest component
    largest_component = max(components, key=len)
    
    if len(largest_component) <= 1:
        return 0
    
    # Create edges for MST of the largest component
    component_set = set(largest_component)
    mst_edges = []
    
    for u, v, w in edges:
        if u in component_set and v in component_set:
            mst_edges.append((w, u, v))
    
    # Sort edges by weight for Kruskal's algorithm
    mst_edges.sort()
    
    # Union-Find for MST
    parent = list(range(n))
    
    def find(x):
        if parent[x] != x:
            parent[x] = find(parent[x])
        return parent[x]
    
    mst_cost = 0
    edges_used = 0
    
    for w, u, v in mst_edges:
        pu, pv = find(u), find(v)
        
        if pu != pv:
            parent[pu] = pv
            mst_cost += w
            edges_used += 1
            
            if edges_used == len(largest_component) - 1:
                break
    
    return mst_cost

# Test cases
def test():
    # Test case 1
    edges1 = [
        (0, 6, 15), (0, 1, 10), (1, 2, 20), (1, 3, 30),
        (2, 3, 40), (2, 5, 50), (2, 4, 60)
    ]
    result1 = solve_atlanta(7, 7, edges1)
    print(f"Test 1: {result1} (expected: 50)")
    
    # Test case 2
    edges2 = [
        (0, 4, 70), (0, 1, 60), (0, 3, 10), (0, 2, 20),
        (1, 5, 50), (1, 2, 30), (1, 3, 80), (2, 3, 40)
    ]
    result2 = solve_atlanta(6, 8, edges2)
    print(f"Test 2: {result2} (expected: 60)")

if __name__ == "__main__":
    test()
