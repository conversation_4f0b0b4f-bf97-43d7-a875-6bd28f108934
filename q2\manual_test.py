# Manual verification of the algorithm

def calculate_cost(n, arr, water_level):
    cost = 0
    mid = n // 2
    
    print(f"Testing water level {water_level}:")
    target_pattern = []
    
    for i in range(n):
        if i <= mid:
            target_height = water_level + (mid - i)
        else:
            target_height = water_level + (i - mid)
        target_pattern.append(target_height)
        cost += abs(arr[i] - target_height)
    
    print(f"  Original: {arr}")
    print(f"  Target:   {target_pattern}")
    print(f"  Cost:     {cost}")
    return cost

# Test Sample 1 manually
print("=== Sample 1 ===")
n1 = 3
a1 = [1, 2, 3]
b1 = [3, 2, 2]

print("East-West direction:")
cost_a_2 = calculate_cost(n1, a1, 2)
print("\nNorth-South direction:")
cost_b_2 = calculate_cost(n1, b1, 2)
print(f"Total cost for water level 2: {cost_a_2 + cost_b_2}")

print("\n=== Sample 2 ===")
n2 = 5
a2 = [2, 3, 0, 1, 4]
b2 = [3, 3, 2, 3, 1]

print("East-West direction:")
cost_a_2_s2 = calculate_cost(n2, a2, 2)
print("\nNorth-South direction:")
cost_b_2_s2 = calculate_cost(n2, b2, 2)
print(f"Total cost for water level 2: {cost_a_2_s2 + cost_b_2_s2}")

print("\nEast-West direction (water level 3):")
cost_a_3_s2 = calculate_cost(n2, a2, 3)
print("\nNorth-South direction (water level 3):")
cost_b_3_s2 = calculate_cost(n2, b2, 3)
print(f"Total cost for water level 3: {cost_a_3_s2 + cost_b_3_s2}")
