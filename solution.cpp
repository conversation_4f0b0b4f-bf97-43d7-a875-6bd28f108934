#include <bits/stdc++.h>
using namespace std;

class UnionFind {
public:
    vector<int> parent, rank;
    
    UnionFind(int n) {
        parent.resize(n);
        rank.resize(n, 0);
        for (int i = 0; i < n; i++) {
            parent[i] = i;
        }
    }
    
    int find(int x) {
        if (parent[x] != x) {
            parent[x] = find(parent[x]);
        }
        return parent[x];
    }
    
    bool unite(int x, int y) {
        int px = find(x), py = find(y);
        if (px == py) return false;
        
        if (rank[px] < rank[py]) {
            parent[px] = py;
        } else if (rank[px] > rank[py]) {
            parent[py] = px;
        } else {
            parent[py] = px;
            rank[px]++;
        }
        return true;
    }
};

void bridgeUtil(int u, vector<vector<int>>& adj, vector<bool>& visited, 
                vector<int>& disc, vector<int>& low, vector<int>& parent, 
                vector<pair<int,int>>& bridges, int& time) {
    visited[u] = true;
    disc[u] = low[u] = ++time;
    
    for (int v : adj[u]) {
        if (!visited[v]) {
            parent[v] = u;
            bridgeUtil(v, adj, visited, disc, low, parent, bridges, time);
            
            low[u] = min(low[u], low[v]);
            
            if (low[v] > disc[u]) {
                bridges.push_back({min(u,v), max(u,v)});
            }
        } else if (v != parent[u]) {
            low[u] = min(low[u], disc[v]);
        }
    }
}

vector<pair<int,int>> findBridges(int n, vector<vector<int>>& adj) {
    vector<bool> visited(n, false);
    vector<int> disc(n), low(n), parent(n);
    vector<pair<int,int>> bridges;
    int time = 0;
    
    for (int i = 0; i < n; i++) {
        parent[i] = -1;
    }
    
    for (int i = 0; i < n; i++) {
        if (!visited[i]) {
            bridgeUtil(i, adj, visited, disc, low, parent, bridges, time);
        }
    }
    
    return bridges;
}

int getCheapestResult(int n, int k, int arr[][3]) {
    // Build adjacency list
    vector<vector<int>> adj(n);
    vector<tuple<int,int,int>> edges;
    
    for (int i = 0; i < k; i++) {
        int u = arr[i][0], v = arr[i][1], w = arr[i][2];
        adj[u].push_back(v);
        adj[v].push_back(u);
        edges.push_back({w, u, v});
    }
    
    // Find bridges
    vector<pair<int,int>> bridges = findBridges(n, adj);
    set<pair<int,int>> bridgeSet;
    for (auto& bridge : bridges) {
        bridgeSet.insert(bridge);
    }
    
    // Remove bridge edges and find remaining vertices
    vector<tuple<int,int,int>> nonBridgeEdges;
    for (int i = 0; i < k; i++) {
        int u = arr[i][0], v = arr[i][1], w = arr[i][2];
        pair<int,int> edge = {min(u,v), max(u,v)};
        if (bridgeSet.find(edge) == bridgeSet.end()) {
            nonBridgeEdges.push_back({w, u, v});
        }
    }
    
    // Find connected components using non-bridge edges
    UnionFind uf(n);
    for (auto& edge : nonBridgeEdges) {
        int u = get<1>(edge), v = get<2>(edge);
        uf.unite(u, v);
    }
    
    // Find the largest component
    map<int, vector<int>> components;
    for (int i = 0; i < n; i++) {
        components[uf.find(i)].push_back(i);
    }
    
    vector<int> largestComponent;
    for (auto& comp : components) {
        if (comp.second.size() > largestComponent.size()) {
            largestComponent = comp.second;
        }
    }
    
    if (largestComponent.size() <= 1) {
        return 0;
    }
    
    // Create mapping for vertices in largest component
    map<int, int> vertexMap;
    for (int i = 0; i < largestComponent.size(); i++) {
        vertexMap[largestComponent[i]] = i;
    }
    
    // Filter edges for largest component and sort by weight
    vector<tuple<int,int,int>> componentEdges;
    for (int i = 0; i < k; i++) {
        int u = arr[i][0], v = arr[i][1], w = arr[i][2];
        if (vertexMap.count(u) && vertexMap.count(v)) {
            componentEdges.push_back({w, vertexMap[u], vertexMap[v]});
        }
    }
    
    sort(componentEdges.begin(), componentEdges.end());
    
    // Kruskal's algorithm for MST
    UnionFind mstUF(largestComponent.size());
    int mstCost = 0;
    int edgesAdded = 0;
    
    for (auto& edge : componentEdges) {
        int w = get<0>(edge), u = get<1>(edge), v = get<2>(edge);
        if (mstUF.unite(u, v)) {
            mstCost += w;
            edgesAdded++;
            if (edgesAdded == largestComponent.size() - 1) {
                break;
            }
        }
    }
    
    return mstCost;
}

int main() {
    int n, k;
    cin >> n >> k;
    int arr[k][3];
    for (int i = 0; i < k; ++i) {
        cin >> arr[i][0];
        cin >> arr[i][1];
        cin >> arr[i][2];
    }
    int result = getCheapestResult(n, k, arr);
    cout << result;
    return 0;
}
